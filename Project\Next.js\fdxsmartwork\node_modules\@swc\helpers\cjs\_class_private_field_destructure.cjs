"use strict";

var _class_apply_descriptor_destructure = require("./_class_apply_descriptor_destructure.cjs");
var _class_extract_field_descriptor = require("./_class_extract_field_descriptor.cjs");

function _class_private_field_destructure(receiver, privateMap) {
    var descriptor = _class_extract_field_descriptor._(receiver, privateMap, "set");
    return _class_apply_descriptor_destructure._(receiver, descriptor);
}
exports._ = _class_private_field_destructure;
